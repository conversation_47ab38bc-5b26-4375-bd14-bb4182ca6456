{"name": "syspos-backend", "version": "0.1.0", "private": true, "main": "dist/index.js", "scripts": {"build": "tsc -p .", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "lint": "", "migrate:make": "knex migrate:make --knexfile knexfile.cjs", "migrate:latest": "knex migrate:latest --knexfile knexfile.cjs", "migrate:rollback": "knex migrate:rollback --knexfile knexfile.cjs", "hasura:console": "cd hasura && /usr/local/bin/hasura console", "hasura:export": "cd hasura && /usr/local/bin/hasura metadata export", "hasura:apply": "cd hasura && /usr/local/bin/hasura metadata apply", "hasura:reload": "cd hasura && /usr/local/bin/hasura metadata reload", "hasura:clear": "cd hasura && /usr/local/bin/hasura metadata clear"}, "dependencies": {"@types/pg": "^8.15.5", "express": "^4.18.2", "knex": "^3.1.0", "pg": "^8.16.3"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.1", "hasura-cli": "^2.38.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.6"}}